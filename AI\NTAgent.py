"""
Requirement.txt
===============

pip install google-generativeai paramiko python-dotenv


"""

import os
import platform
import subprocess
import paramiko
from google import genai
from google.genai import types
from dotenv import load_dotenv
from getpass import getpass

# --- Step 1: Load API Key ---
# Load environment variables from .env file
load_dotenv()
GEMINI_API_KEY = os.getenv("GOOGLE_API_KEY")
if not GEMINI_API_KEY:
    GEMINI_API_KEY = getpass('Enter your Google API key: ')


# --- Step 2: Define Local Tools (Python Functions) ---
# These are the functions the AI agent can choose to call.

def ping_device(hostname: str) -> str:
    """
    Pings a network device to check if it is online and returns the output.
    This is useful for checking basic connectivity.
    """
    print(f"--- Calling Tool: ping_device(hostname='{hostname}') ---")
    try:
        # Determine the correct ping command based on the OS
        param = '-n' if platform.system().lower() == 'windows' else '-c'
        command = ['ping', param, '4', hostname]

        # Execute the command
        result = subprocess.run(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            timeout=20 # 20 second timeout
        )

        # Check the return code to see if the ping was successful
        if result.returncode == 0:
            return f"Ping successful.\nOutput:\n{result.stdout}"
        else:
            return f"Ping failed.\nError:\n{result.stderr}"
    except subprocess.TimeoutExpired:
        return "Ping command timed out after 20 seconds."
    except Exception as e:
        return f"An error occurred while pinging: {str(e)}"

def traceroute_device(hostname: str) -> str:
    """
    Performs a traceroute to a network device to show the path packets take.
    This is useful for diagnosing routing issues.
    """
    print(f"--- Calling Tool: traceroute_device(hostname='{hostname}') ---")
    try:
        # Determine the correct traceroute command based on the OS
        command = 'tracert' if platform.system().lower() == 'windows' else 'traceroute'
        
        # Execute the command
        result = subprocess.run(
            [command, hostname],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            timeout=60 # 60 second timeout
        )
        
        if result.returncode == 0 and result.stdout:
            return f"Traceroute successful.\nOutput:\n{result.stdout}"
        else:
            # Sometimes traceroute prints to stderr even on success
            output = result.stdout or result.stderr
            return f"Traceroute completed.\nOutput:\n{output}"
    except subprocess.TimeoutExpired:
        return "Traceroute command timed out after 60 seconds."
    except Exception as e:
        return f"An error occurred during traceroute: {str(e)}"

def ssh_to_device(hostname: str, username: str, command: str) -> str:
    """
    Connects to a network device via SSH and executes a single command.
    Requires passwordless SSH (key-based authentication) to be pre-configured.
    """
    print(f"--- Calling Tool: ssh_to_device(hostname='{hostname}', username='{username}', command='{command}') ---")
    
    # !!! SECURITY WARNING !!!
    # This function executes commands on a remote device.
    # In a real-world scenario, you MUST implement strict validation and sandboxing.
    # This example assumes a trusted environment and pre-configured key-based auth.
    
    if not all([hostname, username, command]):
        return "Error: hostname, username, and command are all required for SSH."

    try:
        client = paramiko.SSHClient()
        # In a real app, you would verify the host key, not auto-add it.
        client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        
        print(f"Attempting SSH connection to {username}@{hostname}...")
        # Assuming SSH key authentication is set up. Timeout is 10 seconds.
        client.connect(hostname, username=username, timeout=10)
        
        print(f"Executing command: '{command}'")
        stdin, stdout, stderr = client.exec_command(command)
        
        output = stdout.read().decode('utf-8')
        error = stderr.read().decode('utf-8')
        
        client.close()
        
        if error:
            return f"Command executed with errors:\n{error}\nOutput:\n{output}"
        return f"Command executed successfully:\n{output}"
        
    except paramiko.AuthenticationException:
        return "SSH Authentication Failed. Ensure your SSH key is correctly set up for passwordless login."
    except Exception as e:
        return f"An SSH error occurred: {str(e)}"

def user_name() -> str:
    return "Miguel Guzman Lorenzo"


ping_tool_schema = {
    "name": "ping_device",
    "description": "Pings a network device to check if it is online and returns the output. This is useful for checking basic connectivity.",
    "parameters": {
        "type": "object",
        "properties": {
            "hostname": {
                "type": "string",
                "description": "The hostname or IP address of the device to ping.",
            },
        },
        "required": ["hostname"]
    },
}
traceroute_tool_schema = {
    "name":"traceroute_device",
    "description":"Performs a traceroute to a network device to show the path packets take. This is useful for diagnosing routing issues.",
    "parameters":{
        "type": "object",
        "properties": {
            "hostname": {
                "type": "string",
                "description": "The hostname or IP address of the device to traceroute."
            }
        },
        "required": ["hostname"]
    }
}
ssh_tool_schema = {
    "name":"ssh_to_device",
    "description":"Connects to a network device via SSH and executes a single command. Requires passwordless SSH (key-based authentication) to be pre-configured.",
    "parameters":{
        "type": "object",
        "properties": {
            "hostname": {
                "type": "string",
                "description": "The hostname or IP address of the device to connect to."
            },
            "username": {
                "type": "string",
                "description": "The username to use for SSH authentication."
            },
            "command": {
                "type": "string",
                "description": "The command to execute on the remote device."
            }
        },
        "required": ["hostname", "username", "command"]
    }
}

# --- Step 3: The Main Agent Logic ---
client = genai.Client(api_key=GEMINI_API_KEY)
available_tools = [ping_tool_schema, traceroute_tool_schema, ssh_tool_schema,]                       
tools = types.Tool(function_declarations=[available_tools])
config = types.GenerateContentConfig(tools=[tools])

def main():
    """
    The main function to run the network AI agent.
    """

    print("Network AI Agent Initialized. Type 'exit' to quit.")
    print("Example: 'Is ******* online?'")
    print("Example: 'show me the path to cloudflare.com'")
    print("Example: 'ssh to my-server as user admin and run uptime'")
    
    # Start a chat session
    

    while True:
        # Get user input from the console
        user_prompt = input("\n👤 You: ")
        if user_prompt.lower() == 'exit':
            print("🤖 Agent signing off. Goodbye!")
            break

        print("🤖 Gemini is thinking...")
        
        try:

            # Send request with function declarations
            response = client.models.generate_content(
                model="gemini-2.5-flash",
                contents=user,
                config=config,
            )

            # Check for a function call
            if response.candidates[0].content.parts[0].function_call:
                function_call = response.candidates[0].content.parts[0].function_call
                print(f"Function to call: {function_call.name}")
                print(f"Arguments: {function_call.args}")
                #  In a real app, you would call your function here:
                #  result = schedule_meeting(**function_call.args)
            else:
                print("No function call found in the response.")
                print(response.text)

        except Exception as e:
            print(f"\nAn error occurred: {e}")


if __name__ == "__main__":
    main()